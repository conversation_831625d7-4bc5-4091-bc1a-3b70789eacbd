{"version": "0.2.0", "configurations": [{"type": "java", "name": "Main", "request": "launch", "mainClass": "Main", "projectName": "DBMSCollege_1d795361"}, {"type": "java", "name": "Main", "request": "launch", "mainClass": "Main", "projectName": "MusicStreamingProject_307d4d7a"}, {"type": "java", "name": "Main", "request": "launch", "mainClass": "Main", "projectName": "MusicStreamingProject", "classPaths": ["lib/mysql-connector-j-9.3.0.jar"]}, {"type": "java", "name": "TestMusicApp", "request": "launch", "mainClass": "TestMusicApp", "projectName": "MusicStreamingProject", "classPaths": ["lib/mysql-connector-j-9.3.0.jar"]}, {"type": "java", "name": "TestRelationships", "request": "launch", "mainClass": "TestRelationships", "projectName": "MusicStreamingProject", "classPaths": ["lib/mysql-connector-j-9.3.0.jar"]}]}