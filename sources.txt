﻿C:\Projects\Random_college_stuff\MusicStreamingProject\src\Main.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\MusicStreamingApp.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\TestGUI.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\TestMusicApp.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\TestRelationships.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\AlbumController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\ArtistController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\AwardController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\GenreController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\RelationshipController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\SearchController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\controller\SongController.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\dao\AlbumDAO.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\dao\ArtistDAO.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\dao\AwardDAO.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\dao\GenreDAO.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\dao\SongDAO.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\database\DatabaseConnection.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\MainWindow.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\demo\CompactLayoutDemo.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\AlbumDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\AlbumSongDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\ArtistAwardDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\ArtistDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\AwardDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\GenreDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\PerformanceDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\SongDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\dialogs\SongGenreDialog.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\AlbumSongTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\AlbumTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\ArtistAwardTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\ArtistTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\AwardTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\GenreTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\PerformanceTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\SongGenreTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\models\SongTableModel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\AlbumPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\ArtistPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\AwardPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\DashboardPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\GenrePanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\RelationshipPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\SearchPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\panels\SongPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\utils\BeautifulPanel.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\utils\IconManager.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\utils\LayoutHelper.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\gui\utils\UIConstants.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\model\Album.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\model\Artist.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\model\Award.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\model\Genre.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\model\Song.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\service\MusicService.java
C:\Projects\Random_college_stuff\MusicStreamingProject\src\util\InputHelper.java
